[{"__type__": "cc.Prefab", "_name": "Item7", "_objFlags": 0, "__editorExtras__": {}, "_native": "", "data": {"__id__": 1}, "optimizationPolicy": 0, "persistent": false}, {"__type__": "cc.Node", "_name": "Item7", "_objFlags": 0, "__editorExtras__": {}, "_parent": null, "_children": [], "_active": true, "_components": [{"__id__": 2}, {"__id__": 4}, {"__id__": 6}, {"__id__": 8}, {"__id__": 10}], "_prefab": {"__id__": 12}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 1}, "_enabled": true, "__prefab": {"__id__": 3}, "_contentSize": {"__type__": "cc.Size", "width": 81, "height": 66}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "439QvN4yZEsIwecpUTW6T2"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 1}, "_enabled": true, "__prefab": {"__id__": 5}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "daeea92d-8968-43df-b0b7-987e1cd0b6f9@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "deTPAhvABB6YPaARvQje83"}, {"__type__": "422c98TrkdFIptzqSRWe1kP", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 1}, "_enabled": true, "__prefab": {"__id__": 7}, "sprite": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "37kCcDt3hL2qDxOmADj9ff"}, {"__type__": "cc.PolygonCollider2D", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 1}, "_enabled": true, "__prefab": {"__id__": 9}, "tag": 0, "_group": 1, "_density": 1, "_sensor": false, "_friction": 0.2, "_restitution": 0, "_offset": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_points": [{"__type__": "cc.Vec2", "x": -10.76582278481013, "y": 33}, {"__type__": "cc.Vec2", "x": -21.018987341772153, "y": 29.90625}, {"__type__": "cc.Vec2", "x": -28.19620253164557, "y": 25.78125}, {"__type__": "cc.Vec2", "x": -34.348101265822784, "y": 19.59375}, {"__type__": "cc.Vec2", "x": -35.37341772151899, "y": 15.46875}, {"__type__": "cc.Vec2", "x": -39.4746835443038, "y": 12.375}, {"__type__": "cc.Vec2", "x": -40.5, "y": 2.0625}, {"__type__": "cc.Vec2", "x": -38.449367088607595, "y": -20.625}, {"__type__": "cc.Vec2", "x": -27.17088607594937, "y": -27.84375}, {"__type__": "cc.Vec2", "x": -19.99367088607595, "y": -28.875}, {"__type__": "cc.Vec2", "x": -18.968354430379748, "y": -30.9375}, {"__type__": "cc.Vec2", "x": -3.588607594936711, "y": -30.9375}, {"__type__": "cc.Vec2", "x": -0.5126582278481067, "y": -33}, {"__type__": "cc.Vec2", "x": 17.94303797468354, "y": -33}, {"__type__": "cc.Vec2", "x": 25.120253164556956, "y": -29.90625}, {"__type__": "cc.Vec2", "x": 31.272151898734165, "y": -29.90625}, {"__type__": "cc.Vec2", "x": 36.39873417721519, "y": -27.84375}, {"__type__": "cc.Vec2", "x": 40.5, "y": -22.6875}, {"__type__": "cc.Vec2", "x": 40.5, "y": 3.09375}, {"__type__": "cc.Vec2", "x": 38.44936708860759, "y": 5.15625}, {"__type__": "cc.Vec2", "x": 36.39873417721519, "y": 13.40625}, {"__type__": "cc.Vec2", "x": 30.246835443037966, "y": 19.59375}, {"__type__": "cc.Vec2", "x": 24.094936708860757, "y": 22.6875}, {"__type__": "cc.Vec2", "x": 7.689873417721515, "y": 24.75}, {"__type__": "cc.Vec2", "x": 5.639240506329109, "y": 26.8125}, {"__type__": "cc.Vec2", "x": 2.563291139240505, "y": 26.8125}, {"__type__": "cc.Vec2", "x": 2.563291139240505, "y": 28.875}, {"__type__": "cc.Vec2", "x": -0.5126582278481067, "y": 31.96875}], "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "307Lku2AtK+4g2ZGn0kX3Z"}, {"__type__": "cc.RigidBody2D", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 1}, "_enabled": true, "__prefab": {"__id__": 11}, "enabledContactListener": false, "bullet": false, "awakeOnLoad": true, "_group": 1, "_type": 2, "_allowSleep": true, "_gravityScale": 1, "_linearDamping": 0, "_angularDamping": 0, "_linearVelocity": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_angularVelocity": 0, "_fixedRotation": false, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "5b/E12ZRlFeLaErXfjuYFi"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "c46/YsCPVOJYA4mWEpNYRx", "instance": null, "targetOverrides": null}]