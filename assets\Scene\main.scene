[{"__type__": "cc.SceneAsset", "_name": "main", "_objFlags": 0, "__editorExtras__": {}, "_native": "", "scene": {"__id__": 1}}, {"__type__": "cc.Scene", "_name": "main", "_objFlags": 0, "__editorExtras__": {}, "_parent": null, "_children": [{"__id__": 2}], "_active": true, "_components": [], "_prefab": {"__id__": 71}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "autoReleaseAssets": false, "_globals": {"__id__": 72}, "_id": "5446d473-f651-4b87-99cd-973d12282be1"}, {"__type__": "cc.Node", "_name": "<PERSON><PERSON>", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 1}, "_children": [{"__id__": 3}, {"__id__": 5}, {"__id__": 9}, {"__id__": 36}, {"__id__": 54}, {"__id__": 58}], "_active": true, "_components": [{"__id__": 67}, {"__id__": 68}, {"__id__": 69}, {"__id__": 70}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 375, "y": 724, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "beI88Z2HpFELqR4T5EMHpg"}, {"__type__": "cc.Node", "_name": "Camera", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 2}, "_children": [], "_active": true, "_components": [{"__id__": 4}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 1000}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "ebFwiq8gBFaYpqYbdoDODe"}, {"__type__": "cc.Camera", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 3}, "_enabled": true, "__prefab": null, "_projection": 0, "_priority": 0, "_fov": 45, "_fovAxis": 0, "_orthoHeight": 724, "_near": 0, "_far": 2000, "_color": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_depth": 1, "_stencil": 0, "_clearFlags": 7, "_rect": {"__type__": "cc.Rect", "x": 0, "y": 0, "width": 1, "height": 1}, "_aperture": 19, "_shutter": 7, "_iso": 0, "_screenScale": 1, "_visibility": 1108344832, "_targetTexture": null, "_postProcess": null, "_usePostProcess": false, "_cameraType": -1, "_trackingType": 0, "_id": "63WIch3o5BEYRlXzTT0oWc"}, {"__type__": "cc.Node", "_name": "BG", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 2}, "_children": [], "_active": true, "_components": [{"__id__": 6}, {"__id__": 7}, {"__id__": 8}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "628xc34YxFMa2u+e+gOB57"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 5}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "02fe4a00-0f5f-4f93-8969-bfd155700053@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "f3cIIZgIZGAoe8rorA6lcm"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 5}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 750, "height": 1560}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "34l45UIxJCNJ+bLAGWmvQI"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 5}, "_enabled": true, "__prefab": null, "_alignFlags": 17, "_target": null, "_left": 0, "_right": 0, "_top": -56, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 1448, "_alignMode": 2, "_lockFlags": 0, "_id": "145ZCa1YxPb4n+yYL6Z0it"}, {"__type__": "cc.Node", "_name": "Top", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 2}, "_children": [{"__id__": 10}, {"__id__": 22}], "_active": true, "_components": [{"__id__": 34}, {"__id__": 35}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 647, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "c9pHozbeBP1qtH0S7ua0tI"}, {"__type__": "cc.Node", "_name": "<PERSON><PERSON>", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 9}, "_children": [{"__id__": 11}, {"__id__": 14}], "_active": true, "_components": [{"__id__": 17}, {"__id__": 18}, {"__id__": 19}, {"__id__": 20}, {"__id__": 21}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 313, "y": 32, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "31TVnQ+fREZ4RRr0/cB6fp"}, {"__type__": "cc.Node", "_name": "Icon", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 10}, "_children": [], "_active": true, "_components": [{"__id__": 12}, {"__id__": 13}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 1.5389999999999873, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "ddFHp6Do9PdIPwOBlPv3nX"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 11}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 43, "height": 43}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "57ULsvAWFIPav91Rnex3Dp"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 11}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "55ee4376-1884-450f-8a05-a51c248e004f@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_useGrayscale": false, "_atlas": null, "_id": "06vl4O/QFJOYkT9vkXP+T0"}, {"__type__": "cc.Node", "_name": "AudioSource", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 10}, "_children": [], "_active": true, "_components": [{"__id__": 15}, {"__id__": 16}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "45qAyM/IxOdY6S1il4xPV/"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 14}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 56, "height": 56}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "69sFocYkBA17vMJWIWeLWK"}, {"__type__": "cc.AudioSource", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 14}, "_enabled": true, "__prefab": null, "_clip": {"__uuid__": "f26b2db3-e091-4535-bcc5-c5136892c591", "__expectedType__": "cc.AudioClip"}, "_loop": true, "_playOnAwake": false, "_volume": 1, "_id": "e861BQF0RGWIGzQaHLEfrq"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 10}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 60, "height": 60}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "99jWKbeTpAEoZ6/bIizFV8"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 10}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "11b97d68-b017-4062-9c2f-b8768da26d61@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_useGrayscale": false, "_atlas": null, "_id": "51o0zOTJxD3a+0QFI0HA9d"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 10}, "_enabled": true, "__prefab": null, "clickEvents": [], "_interactable": true, "_transition": 2, "_normalColor": {"__type__": "cc.Color", "r": 214, "g": 214, "b": 214, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_normalSprite": {"__uuid__": "11b97d68-b017-4062-9c2f-b8768da26d61@f9941", "__expectedType__": "cc.SpriteFrame"}, "_hoverSprite": null, "_pressedSprite": null, "_disabledSprite": null, "_duration": 0.1, "_zoomScale": 1.2, "_target": {"__id__": 10}, "_id": "53nUR4nChAEbwzqw54uEiX"}, {"__type__": "28ef5aVTKRGZ727pgVT1O52", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 10}, "_enabled": true, "__prefab": null, "bgm": {"__id__": 16}, "iconNode": {"__id__": 13}, "playImg": {"__uuid__": "55ee4376-1884-450f-8a05-a51c248e004f@f9941", "__expectedType__": "cc.SpriteFrame"}, "stopImg": {"__uuid__": "b03942ad-25d7-4280-96e0-fb36dde917a6@f9941", "__expectedType__": "cc.SpriteFrame"}, "rotateSpeed": 3, "_id": "0damraud5CRZCcC9E/f2hl"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 10}, "_enabled": true, "__prefab": null, "_alignFlags": 33, "_target": null, "_left": 0, "_right": 32, "_top": 13, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 40, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 0, "_id": "9ctjpxmr1EopXazz8Z8j8O"}, {"__type__": "cc.Node", "_name": "CountDown", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 9}, "_children": [{"__id__": 23}, {"__id__": 26}, {"__id__": 29}], "_active": true, "_components": [{"__id__": 32}, {"__id__": 33}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0.7150000000000034, "y": -39.405, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "c7SieivtxJbLvi2Kih0irr"}, {"__type__": "cc.Node", "_name": "Icon", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 22}, "_children": [], "_active": true, "_components": [{"__id__": 24}, {"__id__": 25}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": -135.553, "y": 1.463, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "c8talbvfNN5okTi5SDDKjH"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 23}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 40, "height": 62}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "0bNxXlNbRIyJkPUIxVH8UG"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 23}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "4af90eab-b24f-4922-8e8e-522445b11584@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_useGrayscale": false, "_atlas": null, "_id": "aegkN79LRC07g+2ni78zfL"}, {"__type__": "cc.Node", "_name": "Label-da<PERSON><PERSON><PERSON>", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 22}, "_children": [], "_active": true, "_components": [{"__id__": 27}, {"__id__": 28}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": -19.818, "y": 1.3239999999999998, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "95C+QzIAdBgL4vkbXmAJY/"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 26}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 160, "height": 50.4}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "40tqNknsVFY5mn/Jqlzwoo"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 26}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "倒计时：", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 40, "_fontSize": 40, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 40, "_overflow": 0, "_enableWrapText": true, "_font": null, "_isSystemFontUsed": true, "_spacingX": 0, "_isItalic": false, "_isBold": true, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": "4a0Si5ztdK3qDDpsVzThPI"}, {"__type__": "cc.Node", "_name": "Label", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 22}, "_children": [], "_active": true, "_components": [{"__id__": 30}, {"__id__": 31}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 98.8, "y": -0.14500000000000002, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 0.9999999999999999, "y": 0.9999999999999999, "z": 0.9999999999999999}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "5eFc16qOBIhayy1IWFTNgo"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 29}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 102.3046875, "height": 50.4}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "feZhbXGt9HSIiRN7PxWIK0"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 29}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "00:00", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 40, "_fontSize": 40, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 40, "_overflow": 0, "_enableWrapText": true, "_font": null, "_isSystemFontUsed": true, "_spacingX": 0, "_isItalic": false, "_isBold": true, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 155, "g": 62, "b": 0, "a": 122}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 1.76, "y": 0.88}, "_shadowBlur": 0, "_id": "89GvEb9rRMi7R0iW1sdxRQ"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 22}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 336, "height": 84}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "beU5CrA05DK6pi1dqIX+wK"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 22}, "_enabled": true, "__prefab": null, "_alignFlags": 9, "_target": null, "_left": 207.715, "_right": 0, "_top": 72.405, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 0, "_id": "cc7LiKWiJGhpaXGE/SQ9XH"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 9}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 750, "height": 150}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "aav4xHCpxOootUwLHNqy5t"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 9}, "_enabled": true, "__prefab": null, "_alignFlags": 17, "_target": null, "_left": 0, "_right": 0, "_top": 2, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 0, "_id": "68JonMIrZIMY/BJdfuOBJR"}, {"__type__": "cc.Node", "_name": "Blocks", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 2}, "_children": [{"__id__": 37}, {"__id__": 39}, {"__id__": 41}, {"__id__": 43}, {"__id__": 45}, {"__id__": 47}, {"__id__": 49}], "_active": true, "_components": [{"__id__": 51}, {"__id__": 52}, {"__id__": 53}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 501.55999999999995, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "26S/ZuKXVLHYeBju//uUsj"}, {"__type__": "cc.Node", "_name": "block-0", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 36}, "_children": [], "_active": true, "_components": [{"__id__": 38}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": -286.068, "y": 3.478, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "4fxrQaW5JKwb6wZ9hrzrBD"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 37}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 80, "height": 82}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "f2DKkguFBFjI/ZmFZQPKza"}, {"__type__": "cc.Node", "_name": "block-1", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 36}, "_children": [], "_active": true, "_components": [{"__id__": 40}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": -191.065, "y": 3.478, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "dbshuHPAxJTqlJ6zkk2lbx"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 39}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 80, "height": 82}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "cf0+ykYgBCVYD5VDmFQ9nK"}, {"__type__": "cc.Node", "_name": "block-2", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 36}, "_children": [], "_active": true, "_components": [{"__id__": 42}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": -95.015, "y": 3.478, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "02UnWOIUJAJp6S1R5Dfb6d"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 41}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 80, "height": 82}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "f4OOX0zHNCuauEH2XQIkUV"}, {"__type__": "cc.Node", "_name": "block-3", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 36}, "_children": [], "_active": true, "_components": [{"__id__": 44}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 3.478, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "e6wBC/rrpES4hZDAb87c+E"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 43}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 80, "height": 82}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "b98+DxPRlDfZXptcqQDnyY"}, {"__type__": "cc.Node", "_name": "block-4", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 36}, "_children": [], "_active": true, "_components": [{"__id__": 46}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 94.261, "y": 3.478, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "2er1XI7K1CjL33OtueCKaK"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 45}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 80, "height": 82}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "07XYtGZUZDOK9veRvp5luR"}, {"__type__": "cc.Node", "_name": "block-5", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 36}, "_children": [], "_active": true, "_components": [{"__id__": 48}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 190.94, "y": 3.478, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "39hQOjS81P1Ip9xoG7tfwa"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 47}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 80, "height": 82}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "45jMncqWNBEZgY04ilD3sa"}, {"__type__": "cc.Node", "_name": "block-6", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 36}, "_children": [], "_active": true, "_components": [{"__id__": 50}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 283.512, "y": 3.478, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "fePePI4hdCg49YojV4Gwlf"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 49}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 80, "height": 82}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "87YqTeOBpIx7g+IR90aI+4"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 36}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 600, "height": 102}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "13yCOENm1MDo86CzvjG65t"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 36}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "19173a32-f55b-4383-8b4c-69d20be89391@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "09ELGieFNEdaP8PxYGiK0h"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 36}, "_enabled": true, "__prefab": null, "_alignFlags": 17, "_target": null, "_left": 0, "_right": 0, "_top": 171.44000000000005, "_bottom": 1174.56, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 102, "_alignMode": 2, "_lockFlags": 0, "_id": "92zmq/jf5AIaBLFvNBQzog"}, {"__type__": "cc.Node", "_name": "Container", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 2}, "_children": [], "_active": true, "_components": [{"__id__": 55}, {"__id__": 56}, {"__id__": 57}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 105.613, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "bdmP7rroFAeJ2+Wfb5Vecf"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 54}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 750, "height": 1500}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "e5Ag7ZyihAKIJ4d5/+8o+e"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 54}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": null, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "9cmg1eLxxIXaHeNugutGEJ"}, {"__type__": "cc.RigidBody2D", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 54}, "_enabled": true, "__prefab": null, "enabledContactListener": false, "bullet": false, "awakeOnLoad": true, "_group": 1, "_type": 2, "_allowSleep": true, "_gravityScale": 1, "_linearDamping": 0, "_angularDamping": 0, "_linearVelocity": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_angularVelocity": 0, "_fixedRotation": false, "_id": "c5PVf9tXBMqq/G9G1Tzzod"}, {"__type__": "cc.Node", "_name": "Guide", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 2}, "_children": [{"__id__": 59}, {"__id__": 62}], "_active": false, "_components": [{"__id__": 65}, {"__id__": 66}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": -163.455, "y": -208.66700000000003, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "26WzTenrpPnLNIIVnuH/gu"}, {"__type__": "cc.Node", "_name": "main-guide-arrow", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 58}, "_children": [], "_active": true, "_components": [{"__id__": 60}, {"__id__": 61}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": -49.847999999999985, "y": 69.55500000000006, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "f9fPS9EwNLmbBDAkn4GK1w"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 59}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 104, "height": 253}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "635YbTTG9O1bfQ5Ve36uoc"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 59}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "2edaad5c-07ba-44ca-847f-5f087433fb51@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "d3xEHmV5pOmZjI9VUmjbol"}, {"__type__": "cc.Node", "_name": "main-guide-hand", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 58}, "_children": [], "_active": true, "_components": [{"__id__": 63}, {"__id__": 64}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -181.617, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "b3dW2VWk5NFK1Eaz35JdHg"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 62}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 94, "height": 104}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "08mtaghnpPCaaocDUnPu8q"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 62}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "bf26d8bb-19c3-45db-b934-fc487922f52a@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "20/fnb1l5OZ6L1hhvogvMh"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 58}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 360, "height": 500}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "31/rUd0FxBY66XmZpt5X51"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 58}, "_enabled": true, "__prefab": null, "_alignFlags": 4, "_target": null, "_left": 0, "_right": 0, "_top": 0, "_bottom": 265.33299999999997, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 0, "_id": "81hAUBhbNIkpNOwFSvOTLN"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 2}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 1448}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "d6rUX5yfhMlKoWX2bSbawx"}, {"__type__": "cc.<PERSON>", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 2}, "_enabled": true, "__prefab": null, "_cameraComponent": {"__id__": 4}, "_alignCanvasWithScreen": true, "_id": "12O/ljcVlEqLmVm3U2gEOQ"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 2}, "_enabled": true, "__prefab": null, "_alignFlags": 45, "_target": null, "_left": 375, "_right": 375, "_top": 0, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 0, "_id": "c5V1EV8IpMtrIvY1OE9t2u"}, {"__type__": "e1b90/rohdEk4SdmmEZANaD", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 2}, "_enabled": true, "__prefab": null, "labelCountDown": {"__id__": 31}, "prefabSuccessAlert": {"__uuid__": "9a348f54-f9b3-4709-9919-c3577a3eeb7f", "__expectedType__": "cc.Prefab"}, "prefabSuccessWithNoPrizeAlert": {"__uuid__": "51d1e573-2e2c-4f4a-9168-b1b94bf267c6", "__expectedType__": "cc.Prefab"}, "prefabFailAlert": {"__uuid__": "1d43c396-4f6e-4833-a150-aa48d2594b12", "__expectedType__": "cc.Prefab"}, "prefabLotteryAlert": {"__uuid__": "e7708f59-b944-4314-9445-9810a6721d75", "__expectedType__": "cc.Prefab"}, "prefabAdPopup": {"__uuid__": "364f3f39-6e77-4c55-a62b-f04e10de8d20", "__expectedType__": "cc.Prefab"}, "prefabWinningAlert": {"__uuid__": "e3e20f45-253c-4d0d-9ae2-00655b885b6c", "__expectedType__": "cc.Prefab"}, "prefabDidntWinAlert": {"__uuid__": "20eef651-4342-406c-bf03-13bc8fcd19a4", "__expectedType__": "cc.Prefab"}, "buttonAudioEffect": {"__uuid__": "6099aee0-527a-414a-b96d-09474b3fa8e9", "__expectedType__": "cc.AudioClip"}, "clearLineAudioEffect": {"__uuid__": "82a5eac6-4736-4697-980b-6daa16bb3b3a", "__expectedType__": "cc.AudioClip"}, "gameSuccessSound": {"__uuid__": "5f020cf6-0ec6-46bd-b2d6-00d85449467d", "__expectedType__": "cc.AudioClip"}, "gameFailSound": {"__uuid__": "738999af-31bf-4627-9488-cb4f95cc3422", "__expectedType__": "cc.AudioClip"}, "lotterySuccessSound": {"__uuid__": "394efe91-10ed-4664-b8f9-d3a3ecd43659", "__expectedType__": "cc.AudioClip"}, "lotteryFailSound": {"__uuid__": "5e3f637a-3447-4aae-ac56-49dc45361a25", "__expectedType__": "cc.AudioClip"}, "roundSuccessSound": {"__uuid__": "e5b48b3c-d808-4757-a2ea-25a3b8f168bc", "__expectedType__": "cc.AudioClip"}, "roundFailSound": {"__uuid__": "6a0e1753-65c0-44a8-a5c7-c9cf7e411fe9", "__expectedType__": "cc.AudioClip"}, "containerNode": {"__id__": 54}, "blocksNode": {"__id__": 36}, "itemPrefab1": {"__uuid__": "384c84d8-b10c-4452-b562-0b4828d75483", "__expectedType__": "cc.Prefab"}, "itemPrefab2": {"__uuid__": "0dea339e-2063-4b03-a9d2-8cb0c4aea4e8", "__expectedType__": "cc.Prefab"}, "itemPrefab3": {"__uuid__": "9339c87a-bdc9-4bb2-ba45-c5d4b167fc04", "__expectedType__": "cc.Prefab"}, "itemPrefab4": {"__uuid__": "78ea1b83-f856-4345-9549-168f0475fcff", "__expectedType__": "cc.Prefab"}, "itemPrefab5": {"__uuid__": "ecdd263f-11ef-4e43-8875-0516c8c5ddcf", "__expectedType__": "cc.Prefab"}, "itemPrefab6": {"__uuid__": "050977b6-ec04-4085-aaed-8097b944a7ba", "__expectedType__": "cc.Prefab"}, "itemPrefab7": {"__uuid__": "051a402c-d7c1-4763-b3df-ba780837ddb3", "__expectedType__": "cc.Prefab"}, "itemPrefab8": {"__uuid__": "f7c1f57a-9603-4887-a0b9-24ff20ca2375", "__expectedType__": "cc.Prefab"}, "itemPrefab9": {"__uuid__": "16b9ce32-35e0-42d6-b15a-4dab5404c638", "__expectedType__": "cc.Prefab"}, "itemPrefab10": {"__uuid__": "6306fc49-a223-4d0c-9905-7dc6297f280d", "__expectedType__": "cc.Prefab"}, "itemPrefab11": {"__uuid__": "9726da8c-b244-487d-82b6-4d00b1bad7a6", "__expectedType__": "cc.Prefab"}, "itemClearAnimation": {"__uuid__": "e4b5a31b-1c6c-40e3-947b-734e4dd16b3d", "__expectedType__": "cc.Prefab"}, "dropHeight": 800, "dropInterval": 0.1, "groundY": -400, "_id": "c1tt4//A9ELqlGeK+KfAgI"}, {"__type__": "cc.PrefabInfo", "root": null, "asset": null, "fileId": "5446d473-f651-4b87-99cd-973d12282be1", "instance": null, "targetOverrides": null}, {"__type__": "cc.SceneGlobals", "ambient": {"__id__": 73}, "shadows": {"__id__": 74}, "_skybox": {"__id__": 75}, "fog": {"__id__": 76}, "octree": {"__id__": 77}, "skin": {"__id__": 78}, "lightProbeInfo": {"__id__": 79}, "postSettings": {"__id__": 80}, "bakedWithStationaryMainLight": false, "bakedWithHighpLightmap": false}, {"__type__": "cc.AmbientInfo", "_skyColorHDR": {"__type__": "cc.Vec4", "x": 0, "y": 0, "z": 0, "w": 0.520833125}, "_skyColor": {"__type__": "cc.Vec4", "x": 0, "y": 0, "z": 0, "w": 0.520833125}, "_skyIllumHDR": 20000, "_skyIllum": 20000, "_groundAlbedoHDR": {"__type__": "cc.Vec4", "x": 0, "y": 0, "z": 0, "w": 0}, "_groundAlbedo": {"__type__": "cc.Vec4", "x": 0, "y": 0, "z": 0, "w": 0}, "_skyColorLDR": {"__type__": "cc.Vec4", "x": 0.2, "y": 0.5, "z": 0.8, "w": 1}, "_skyIllumLDR": 20000, "_groundAlbedoLDR": {"__type__": "cc.Vec4", "x": 0.2, "y": 0.2, "z": 0.2, "w": 1}}, {"__type__": "cc.ShadowsInfo", "_enabled": false, "_type": 0, "_normal": {"__type__": "cc.Vec3", "x": 0, "y": 1, "z": 0}, "_distance": 0, "_planeBias": 1, "_shadowColor": {"__type__": "cc.Color", "r": 76, "g": 76, "b": 76, "a": 255}, "_maxReceived": 4, "_size": {"__type__": "cc.Vec2", "x": 512, "y": 512}}, {"__type__": "cc.SkyboxInfo", "_envLightingType": 0, "_envmapHDR": null, "_envmap": null, "_envmapLDR": null, "_diffuseMapHDR": null, "_diffuseMapLDR": null, "_enabled": false, "_useHDR": true, "_editableMaterial": null, "_reflectionHDR": null, "_reflectionLDR": null, "_rotationAngle": 0}, {"__type__": "cc.FogInfo", "_type": 0, "_fogColor": {"__type__": "cc.Color", "r": 200, "g": 200, "b": 200, "a": 255}, "_enabled": false, "_fogDensity": 0.3, "_fogStart": 0.5, "_fogEnd": 300, "_fogAtten": 5, "_fogTop": 1.5, "_fogRange": 1.2, "_accurate": false}, {"__type__": "cc.OctreeInfo", "_enabled": false, "_minPos": {"__type__": "cc.Vec3", "x": -1024, "y": -1024, "z": -1024}, "_maxPos": {"__type__": "cc.Vec3", "x": 1024, "y": 1024, "z": 1024}, "_depth": 8}, {"__type__": "cc.SkinInfo", "_enabled": false, "_blurRadius": 0.01, "_sssIntensity": 3}, {"__type__": "cc.LightProbeInfo", "_giScale": 1, "_giSamples": 1024, "_bounces": 2, "_reduceRinging": 0, "_showProbe": true, "_showWireframe": true, "_showConvex": false, "_data": null, "_lightProbeSphereVolume": 1}, {"__type__": "cc.PostSettingsInfo", "_toneMappingType": 0}]