[{"__type__": "cc.Prefab", "_name": "Item1", "_objFlags": 0, "__editorExtras__": {}, "_native": "", "data": {"__id__": 1}, "optimizationPolicy": 0, "persistent": false}, {"__type__": "cc.Node", "_name": "Item1", "_objFlags": 0, "__editorExtras__": {}, "_parent": null, "_children": [], "_active": true, "_components": [{"__id__": 2}, {"__id__": 4}, {"__id__": 6}, {"__id__": 8}, {"__id__": 10}], "_prefab": {"__id__": 12}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 1}, "_enabled": true, "__prefab": {"__id__": 3}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 176}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "439QvN4yZEsIwecpUTW6T2"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 1}, "_enabled": true, "__prefab": {"__id__": 5}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "4e49335a-1a03-4a5b-a335-6e71c521ae33@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "deTPAhvABB6YPaARvQje83"}, {"__type__": "422c98TrkdFIptzqSRWe1kP", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 1}, "_enabled": true, "__prefab": {"__id__": 7}, "sprite": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "37kCcDt3hL2qDxOmADj9ff"}, {"__type__": "cc.PolygonCollider2D", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 1}, "_enabled": true, "__prefab": {"__id__": 9}, "tag": 0, "_group": 1, "_density": 1, "_sensor": false, "_friction": 0.2, "_restitution": 0, "_offset": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_points": [{"__type__": "cc.Vec2", "x": -20, "y": 88}, {"__type__": "cc.Vec2", "x": -24, "y": 87}, {"__type__": "cc.Vec2", "x": -29, "y": 82}, {"__type__": "cc.Vec2", "x": -30, "y": 72}, {"__type__": "cc.Vec2", "x": -27, "y": 71}, {"__type__": "cc.Vec2", "x": -27, "y": 67}, {"__type__": "cc.Vec2", "x": -25, "y": 68}, {"__type__": "cc.Vec2", "x": -23, "y": 66}, {"__type__": "cc.Vec2", "x": -24, "y": 63}, {"__type__": "cc.Vec2", "x": -22, "y": 64}, {"__type__": "cc.Vec2", "x": -23, "y": 62}, {"__type__": "cc.Vec2", "x": -20, "y": 61}, {"__type__": "cc.Vec2", "x": -19, "y": 57}, {"__type__": "cc.Vec2", "x": -25, "y": 55}, {"__type__": "cc.Vec2", "x": -26, "y": 53}, {"__type__": "cc.Vec2", "x": -29, "y": 54}, {"__type__": "cc.Vec2", "x": -28, "y": 52}, {"__type__": "cc.Vec2", "x": -33, "y": 48}, {"__type__": "cc.Vec2", "x": -34, "y": 45}, {"__type__": "cc.Vec2", "x": -33, "y": 40}, {"__type__": "cc.Vec2", "x": -36, "y": 37}, {"__type__": "cc.Vec2", "x": -37, "y": 38}, {"__type__": "cc.Vec2", "x": -35, "y": 40}, {"__type__": "cc.Vec2", "x": -37, "y": 40}, {"__type__": "cc.Vec2", "x": -40, "y": 37}, {"__type__": "cc.Vec2", "x": -40, "y": 35}, {"__type__": "cc.Vec2", "x": -38, "y": 35}, {"__type__": "cc.Vec2", "x": -42, "y": 32}, {"__type__": "cc.Vec2", "x": -42, "y": 30}, {"__type__": "cc.Vec2", "x": -47, "y": 24}, {"__type__": "cc.Vec2", "x": -47, "y": 21}, {"__type__": "cc.Vec2", "x": -50, "y": 16}, {"__type__": "cc.Vec2", "x": -50, "y": -9}, {"__type__": "cc.Vec2", "x": -48, "y": -9}, {"__type__": "cc.Vec2", "x": -45, "y": -15}, {"__type__": "cc.Vec2", "x": -46, "y": -18}, {"__type__": "cc.Vec2", "x": -44, "y": -17}, {"__type__": "cc.Vec2", "x": -44, "y": -20}, {"__type__": "cc.Vec2", "x": -42, "y": -20}, {"__type__": "cc.Vec2", "x": -36, "y": -26}, {"__type__": "cc.Vec2", "x": -37, "y": -28}, {"__type__": "cc.Vec2", "x": -33, "y": -28}, {"__type__": "cc.Vec2", "x": -34, "y": -30}, {"__type__": "cc.Vec2", "x": -31, "y": -29}, {"__type__": "cc.Vec2", "x": -30, "y": -31}, {"__type__": "cc.Vec2", "x": -25, "y": -32}, {"__type__": "cc.Vec2", "x": -25, "y": -34}, {"__type__": "cc.Vec2", "x": -22, "y": -33}, {"__type__": "cc.Vec2", "x": -17, "y": -35}, {"__type__": "cc.Vec2", "x": -10, "y": -35}, {"__type__": "cc.Vec2", "x": -10, "y": -37}, {"__type__": "cc.Vec2", "x": -3, "y": -42}, {"__type__": "cc.Vec2", "x": 6, "y": -41}, {"__type__": "cc.Vec2", "x": 7, "y": -46}, {"__type__": "cc.Vec2", "x": 9, "y": -47}, {"__type__": "cc.Vec2", "x": 7, "y": -54}, {"__type__": "cc.Vec2", "x": 2, "y": -56}, {"__type__": "cc.Vec2", "x": 4, "y": -54}, {"__type__": "cc.Vec2", "x": 1, "y": -54}, {"__type__": "cc.Vec2", "x": 1, "y": -51}, {"__type__": "cc.Vec2", "x": -2, "y": -52}, {"__type__": "cc.Vec2", "x": -3, "y": -50}, {"__type__": "cc.Vec2", "x": -9, "y": -52}, {"__type__": "cc.Vec2", "x": -12, "y": -55}, {"__type__": "cc.Vec2", "x": -14, "y": -72}, {"__type__": "cc.Vec2", "x": -12, "y": -76}, {"__type__": "cc.Vec2", "x": -13, "y": -78}, {"__type__": "cc.Vec2", "x": -10, "y": -79}, {"__type__": "cc.Vec2", "x": -11, "y": -82}, {"__type__": "cc.Vec2", "x": -9, "y": -81}, {"__type__": "cc.Vec2", "x": -7, "y": -83}, {"__type__": "cc.Vec2", "x": -8, "y": -85}, {"__type__": "cc.Vec2", "x": -5, "y": -84}, {"__type__": "cc.Vec2", "x": -4, "y": -87}, {"__type__": "cc.Vec2", "x": -2, "y": -86}, {"__type__": "cc.Vec2", "x": 0, "y": -88}, {"__type__": "cc.Vec2", "x": 8, "y": -88}, {"__type__": "cc.Vec2", "x": 9, "y": -86}, {"__type__": "cc.Vec2", "x": 11, "y": -87}, {"__type__": "cc.Vec2", "x": 12, "y": -84}, {"__type__": "cc.Vec2", "x": 14, "y": -85}, {"__type__": "cc.Vec2", "x": 14, "y": -83}, {"__type__": "cc.Vec2", "x": 20, "y": -77}, {"__type__": "cc.Vec2", "x": 22, "y": -78}, {"__type__": "cc.Vec2", "x": 21, "y": -75}, {"__type__": "cc.Vec2", "x": 23, "y": -74}, {"__type__": "cc.Vec2", "x": 25, "y": -68}, {"__type__": "cc.Vec2", "x": 27, "y": -68}, {"__type__": "cc.Vec2", "x": 26, "y": -65}, {"__type__": "cc.Vec2", "x": 27, "y": -61}, {"__type__": "cc.Vec2", "x": 29, "y": -61}, {"__type__": "cc.Vec2", "x": 28, "y": -57}, {"__type__": "cc.Vec2", "x": 30, "y": -54}, {"__type__": "cc.Vec2", "x": 30, "y": -35}, {"__type__": "cc.Vec2", "x": 28, "y": -33}, {"__type__": "cc.Vec2", "x": 31, "y": -34}, {"__type__": "cc.Vec2", "x": 30, "y": -32}, {"__type__": "cc.Vec2", "x": 35, "y": -28}, {"__type__": "cc.Vec2", "x": 36, "y": -21}, {"__type__": "cc.Vec2", "x": 38, "y": -21}, {"__type__": "cc.Vec2", "x": 37, "y": -19}, {"__type__": "cc.Vec2", "x": 40, "y": -16}, {"__type__": "cc.Vec2", "x": 42, "y": -17}, {"__type__": "cc.Vec2", "x": 41, "y": -14}, {"__type__": "cc.Vec2", "x": 43, "y": -12}, {"__type__": "cc.Vec2", "x": 45, "y": -13}, {"__type__": "cc.Vec2", "x": 44, "y": -10}, {"__type__": "cc.Vec2", "x": 47, "y": -9}, {"__type__": "cc.Vec2", "x": 46, "y": -7}, {"__type__": "cc.Vec2", "x": 48, "y": -7}, {"__type__": "cc.Vec2", "x": 47, "y": -4}, {"__type__": "cc.Vec2", "x": 50, "y": 2}, {"__type__": "cc.Vec2", "x": 50, "y": 20}, {"__type__": "cc.Vec2", "x": 47, "y": 26}, {"__type__": "cc.Vec2", "x": 48, "y": 29}, {"__type__": "cc.Vec2", "x": 46, "y": 29}, {"__type__": "cc.Vec2", "x": 46, "y": 31}, {"__type__": "cc.Vec2", "x": 43, "y": 34}, {"__type__": "cc.Vec2", "x": 44, "y": 36}, {"__type__": "cc.Vec2", "x": 42, "y": 36}, {"__type__": "cc.Vec2", "x": 35, "y": 43}, {"__type__": "cc.Vec2", "x": 36, "y": 45}, {"__type__": "cc.Vec2", "x": 33, "y": 44}, {"__type__": "cc.Vec2", "x": 33, "y": 47}, {"__type__": "cc.Vec2", "x": 30, "y": 46}, {"__type__": "cc.Vec2", "x": 30, "y": 49}, {"__type__": "cc.Vec2", "x": 27, "y": 48}, {"__type__": "cc.Vec2", "x": 28, "y": 50}, {"__type__": "cc.Vec2", "x": 25, "y": 49}, {"__type__": "cc.Vec2", "x": 16, "y": 52}, {"__type__": "cc.Vec2", "x": 14, "y": 59}, {"__type__": "cc.Vec2", "x": 12, "y": 58}, {"__type__": "cc.Vec2", "x": 8, "y": 61}, {"__type__": "cc.Vec2", "x": -2, "y": 61}, {"__type__": "cc.Vec2", "x": -2, "y": 71}, {"__type__": "cc.Vec2", "x": -4, "y": 71}, {"__type__": "cc.Vec2", "x": -5, "y": 77}, {"__type__": "cc.Vec2", "x": -7, "y": 79}, {"__type__": "cc.Vec2", "x": -6, "y": 82}, {"__type__": "cc.Vec2", "x": -8, "y": 81}, {"__type__": "cc.Vec2", "x": -12, "y": 85}, {"__type__": "cc.Vec2", "x": -11, "y": 87}, {"__type__": "cc.Vec2", "x": -14, "y": 86}], "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "3fQqbdYV9HvZ8WmVpC5hFE"}, {"__type__": "cc.RigidBody2D", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 1}, "_enabled": true, "__prefab": {"__id__": 11}, "enabledContactListener": false, "bullet": false, "awakeOnLoad": true, "_group": 1, "_type": 2, "_allowSleep": true, "_gravityScale": 1, "_linearDamping": 0, "_angularDamping": 0, "_linearVelocity": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_angularVelocity": 0, "_fixedRotation": false, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "9e9BmtFFRJfIopQWK6f1t5"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "c46/YsCPVOJYA4mWEpNYRx", "instance": null, "targetOverrides": null}]