[{"__type__": "cc.Prefab", "_name": "Item5", "_objFlags": 0, "__editorExtras__": {}, "_native": "", "data": {"__id__": 1}, "optimizationPolicy": 0, "persistent": false}, {"__type__": "cc.Node", "_name": "Item5", "_objFlags": 0, "__editorExtras__": {}, "_parent": null, "_children": [], "_active": true, "_components": [{"__id__": 2}, {"__id__": 4}, {"__id__": 6}, {"__id__": 8}, {"__id__": 10}], "_prefab": {"__id__": 12}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 1}, "_enabled": true, "__prefab": {"__id__": 3}, "_contentSize": {"__type__": "cc.Size", "width": 181, "height": 221}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "439QvN4yZEsIwecpUTW6T2"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 1}, "_enabled": true, "__prefab": {"__id__": 5}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "0e14b300-170f-407a-b326-1cf6f904fa0f@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "deTPAhvABB6YPaARvQje83"}, {"__type__": "422c98TrkdFIptzqSRWe1kP", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 1}, "_enabled": true, "__prefab": {"__id__": 7}, "sprite": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "37kCcDt3hL2qDxOmADj9ff"}, {"__type__": "cc.PolygonCollider2D", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 1}, "_enabled": true, "__prefab": {"__id__": 9}, "tag": 0, "_group": 1, "_density": 1, "_sensor": false, "_friction": 0.2, "_restitution": 0, "_offset": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_points": [{"__type__": "cc.Vec2", "x": -46.25555555555556, "y": 110.5}, {"__type__": "cc.Vec2", "x": -55.30555555555556, "y": 106.5}, {"__type__": "cc.Vec2", "x": -62.34444444444445, "y": 99.5}, {"__type__": "cc.Vec2", "x": -68.37777777777778, "y": 86.5}, {"__type__": "cc.Vec2", "x": -76.42222222222222, "y": 84.5}, {"__type__": "cc.Vec2", "x": -84.46666666666667, "y": 76.5}, {"__type__": "cc.Vec2", "x": -87.48333333333333, "y": 69.5}, {"__type__": "cc.Vec2", "x": -87.48333333333333, "y": 63.5}, {"__type__": "cc.Vec2", "x": -90.5, "y": 57.5}, {"__type__": "cc.Vec2", "x": -89.49444444444444, "y": 37.5}, {"__type__": "cc.Vec2", "x": -84.46666666666667, "y": 27.5}, {"__type__": "cc.Vec2", "x": -79.4388888888889, "y": 22.5}, {"__type__": "cc.Vec2", "x": -82.45555555555555, "y": 8.5}, {"__type__": "cc.Vec2", "x": -82.45555555555555, "y": -24.5}, {"__type__": "cc.Vec2", "x": -80.44444444444444, "y": -41.5}, {"__type__": "cc.Vec2", "x": -74.41111111111111, "y": -69.5}, {"__type__": "cc.Vec2", "x": -66.36666666666667, "y": -86.5}, {"__type__": "cc.Vec2", "x": -56.31111111111111, "y": -96.5}, {"__type__": "cc.Vec2", "x": -47.26111111111111, "y": -100.5}, {"__type__": "cc.Vec2", "x": -43.23888888888889, "y": -105.5}, {"__type__": "cc.Vec2", "x": -41.22777777777778, "y": -105.5}, {"__type__": "cc.Vec2", "x": -38.21111111111111, "y": -108.5}, {"__type__": "cc.Vec2", "x": -31.172222222222224, "y": -110.5}, {"__type__": "cc.Vec2", "x": -19.105555555555554, "y": -109.5}, {"__type__": "cc.Vec2", "x": -14.077777777777783, "y": -107.5}, {"__type__": "cc.Vec2", "x": -10.055555555555557, "y": -103.5}, {"__type__": "cc.Vec2", "x": 13.072222222222223, "y": -99.5}, {"__type__": "cc.Vec2", "x": 41.22777777777779, "y": -90.5}, {"__type__": "cc.Vec2", "x": 53.29444444444445, "y": -84.5}, {"__type__": "cc.Vec2", "x": 62.344444444444434, "y": -84.5}, {"__type__": "cc.Vec2", "x": 72.4, "y": -80.5}, {"__type__": "cc.Vec2", "x": 79.4388888888889, "y": -73.5}, {"__type__": "cc.Vec2", "x": 82.45555555555555, "y": -66.5}, {"__type__": "cc.Vec2", "x": 83.46111111111111, "y": -58.5}, {"__type__": "cc.Vec2", "x": 86.47777777777776, "y": -55.5}, {"__type__": "cc.Vec2", "x": 90.5, "y": -44.5}, {"__type__": "cc.Vec2", "x": 89.49444444444444, "y": -26.5}, {"__type__": "cc.Vec2", "x": 83.46111111111111, "y": -8.5}, {"__type__": "cc.Vec2", "x": 79.4388888888889, "y": -2.5}, {"__type__": "cc.Vec2", "x": 79.4388888888889, "y": 0.5}, {"__type__": "cc.Vec2", "x": 75.41666666666666, "y": 5.5}, {"__type__": "cc.Vec2", "x": 69.38333333333333, "y": 18.5}, {"__type__": "cc.Vec2", "x": 67.3722222222222, "y": 19.5}, {"__type__": "cc.Vec2", "x": 66.36666666666667, "y": 23.5}, {"__type__": "cc.Vec2", "x": 64.35555555555555, "y": 24.5}, {"__type__": "cc.Vec2", "x": 63.349999999999994, "y": 28.5}, {"__type__": "cc.Vec2", "x": 61.338888888888874, "y": 29.5}, {"__type__": "cc.Vec2", "x": 55.30555555555554, "y": 40.5}, {"__type__": "cc.Vec2", "x": 52.28888888888889, "y": 42.5}, {"__type__": "cc.Vec2", "x": 52.28888888888889, "y": 44.5}, {"__type__": "cc.Vec2", "x": 47.26111111111112, "y": 48.5}, {"__type__": "cc.Vec2", "x": 47.26111111111112, "y": 50.5}, {"__type__": "cc.Vec2", "x": 29.16111111111111, "y": 65.5}, {"__type__": "cc.Vec2", "x": 16.08888888888889, "y": 71.5}, {"__type__": "cc.Vec2", "x": 4.022222222222226, "y": 73.5}, {"__type__": "cc.Vec2", "x": 3.0166666666666657, "y": 81.5}, {"__type__": "cc.Vec2", "x": -1.00555555555556, "y": 85.5}, {"__type__": "cc.Vec2", "x": -6.033333333333331, "y": 86.5}, {"__type__": "cc.Vec2", "x": -13.072222222222223, "y": 83.5}, {"__type__": "cc.Vec2", "x": -13.072222222222223, "y": 90.5}, {"__type__": "cc.Vec2", "x": -15.083333333333329, "y": 96.5}, {"__type__": "cc.Vec2", "x": -18.099999999999994, "y": 99.5}, {"__type__": "cc.Vec2", "x": -18.099999999999994, "y": 101.5}, {"__type__": "cc.Vec2", "x": -25.138888888888886, "y": 107.5}, {"__type__": "cc.Vec2", "x": -32.17777777777778, "y": 110.5}], "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "e6MVel1u5Jsow98IdPgV/5"}, {"__type__": "cc.RigidBody2D", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 1}, "_enabled": true, "__prefab": {"__id__": 11}, "enabledContactListener": false, "bullet": false, "awakeOnLoad": true, "_group": 1, "_type": 2, "_allowSleep": true, "_gravityScale": 1, "_linearDamping": 0, "_angularDamping": 0, "_linearVelocity": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_angularVelocity": 0, "_fixedRotation": false, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "f7Gd+GZBlOlLDAF0PcUnip"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "c46/YsCPVOJYA4mWEpNYRx", "instance": null, "targetOverrides": null}]