import {
  _decorator,
  Component,
  Node,
  Prefab,
  instantiate,
  Vec3,
  RigidBody2D,
  PolygonCollider2D,
  ERigidBody2DType,
  Vec2,
} from "cc"

const { ccclass, property } = _decorator

/**
 * 物品池化系统
 * 管理物品实例的创建、复用和回收，减少GC压力
 */
@ccclass("ItemPool")
export class ItemPool extends Component {
  
  @property({ type: Number, tooltip: "每种物品的预创建数量" })
  preCreateCount: number = 5

  @property({ type: Number, tooltip: "池的最大容量" })
  maxPoolSize: number = 20

  @property({ type: Boolean, tooltip: "是否启用池化" })
  enablePooling: boolean = true

  // 物品池 - 按类型分组
  private itemPools: Map<number, Node[]> = new Map()
  
  // 活跃物品列表
  private activeItems: Set<Node> = new Set()
  
  // 预制体引用
  private itemPrefabs: Prefab[] = []
  
  // 池化统计
  private poolStats = {
    totalCreated: 0,
    totalReused: 0,
    totalRecycled: 0,
    currentActive: 0,
    currentPooled: 0
  }

  start() {
    console.log("ItemPool 初始化完成")
  }

  /**
   * 设置物品预制体
   */
  setItemPrefabs(prefabs: Prefab[]): void {
    this.itemPrefabs = prefabs
    
    if (this.enablePooling) {
      this.preCreateItems()
    }
  }

  /**
   * 预创建物品实例
   */
  private preCreateItems(): void {
    for (let i = 0; i < this.itemPrefabs.length; i++) {
      const prefab = this.itemPrefabs[i]
      if (!prefab) continue

      const pool: Node[] = []
      
      for (let j = 0; j < this.preCreateCount; j++) {
        const item = this.createNewItem(prefab, i)
        this.resetItemToPoolState(item)
        pool.push(item)
      }
      
      this.itemPools.set(i, pool)
      this.poolStats.currentPooled += pool.length
    }
    
    console.log(`预创建完成，总计 ${this.poolStats.currentPooled} 个物品实例`)
  }

  /**
   * 获取物品实例
   */
  getItem(itemType: number): Node | null {
    if (!this.enablePooling) {
      return this.createNewItemDirect(itemType)
    }

    const pool = this.itemPools.get(itemType)
    let item: Node | null = null

    if (pool && pool.length > 0) {
      // 从池中获取
      item = pool.pop()!
      this.poolStats.totalReused++
      this.poolStats.currentPooled--
    } else {
      // 池中没有可用实例，创建新的
      item = this.createNewItemDirect(itemType)
    }

    if (item) {
      this.activeItems.add(item)
      this.poolStats.currentActive++
      this.prepareItemForUse(item, itemType)
    }

    return item
  }

  /**
   * 回收物品实例
   */
  recycleItem(item: Node, itemType: number): void {
    if (!this.activeItems.has(item)) {
      console.warn("尝试回收未激活的物品:", item.name)
      return
    }

    this.activeItems.delete(item)
    this.poolStats.currentActive--

    if (!this.enablePooling) {
      item.destroy()
      return
    }

    const pool = this.itemPools.get(itemType)
    if (pool && pool.length < this.maxPoolSize) {
      // 回收到池中
      this.resetItemToPoolState(item)
      pool.push(item)
      this.poolStats.totalRecycled++
      this.poolStats.currentPooled++
    } else {
      // 池已满，直接销毁
      item.destroy()
    }
  }

  /**
   * 创建新物品实例（直接创建，不使用池）
   */
  private createNewItemDirect(itemType: number): Node | null {
    const prefab = this.itemPrefabs[itemType]
    if (!prefab) {
      console.error(`物品类型 ${itemType} 的预制体不存在`)
      return null
    }

    return this.createNewItem(prefab, itemType)
  }

  /**
   * 创建新物品实例
   */
  private createNewItem(prefab: Prefab, itemType: number): Node {
    const item = instantiate(prefab)
    item.name = `Item_${itemType}_${this.poolStats.totalCreated}`
    
    this.poolStats.totalCreated++
    
    return item
  }

  /**
   * 准备物品供使用
   */
  private prepareItemForUse(item: Node, itemType: number): void {
    // 重置基本属性
    item.active = true
    item.setPosition(0, 0, 0)
    item.setRotationFromEuler(0, 0, 0)
    item.setScale(1, 1, 1)

    // 重置物理组件
    const rigidBody = item.getComponent(RigidBody2D)
    if (rigidBody) {
      rigidBody.type = ERigidBody2DType.Dynamic
      rigidBody.linearVelocity = Vec2.ZERO
      rigidBody.angularVelocity = 0
      rigidBody.wakeUp()
    }

    // 重置碰撞器
    const collider = item.getComponent(PolygonCollider2D)
    if (collider) {
      collider.enabled = true
    }

    console.log(`准备物品供使用: ${item.name}`)
  }

  /**
   * 重置物品到池状态
   */
  private resetItemToPoolState(item: Node): void {
    // 隐藏物品
    item.active = false
    
    // 移除父节点
    if (item.parent) {
      item.removeFromParent()
    }

    // 重置物理状态
    const rigidBody = item.getComponent(RigidBody2D)
    if (rigidBody) {
      rigidBody.type = ERigidBody2DType.Static
      rigidBody.linearVelocity = Vec2.ZERO
      rigidBody.angularVelocity = 0
      rigidBody.sleep()
    }

    // 禁用碰撞器以节省性能
    const collider = item.getComponent(PolygonCollider2D)
    if (collider) {
      collider.enabled = false
    }

    // 重置位置
    item.setPosition(0, 0, 0)
    item.setRotationFromEuler(0, 0, 0)
    item.setScale(1, 1, 1)
  }

  /**
   * 清空所有池
   */
  clearAllPools(): void {
    // 回收所有活跃物品
    const activeItemsArray = Array.from(this.activeItems)
    for (const item of activeItemsArray) {
      if (item.isValid) {
        item.destroy()
      }
    }
    this.activeItems.clear()

    // 销毁池中的物品
    for (const [itemType, pool] of this.itemPools) {
      for (const item of pool) {
        if (item.isValid) {
          item.destroy()
        }
      }
      pool.length = 0
    }
    this.itemPools.clear()

    // 重置统计
    this.poolStats = {
      totalCreated: 0,
      totalReused: 0,
      totalRecycled: 0,
      currentActive: 0,
      currentPooled: 0
    }

    console.log("所有物品池已清空")
  }

  /**
   * 获取池化统计信息
   */
  getPoolStats() {
    return { ...this.poolStats }
  }

  /**
   * 获取指定类型的池状态
   */
  getPoolStatus(itemType: number): { available: number; active: number } {
    const pool = this.itemPools.get(itemType)
    const available = pool ? pool.length : 0
    
    let active = 0
    for (const item of this.activeItems) {
      // 这里需要一种方法来识别物品类型，可以通过名称或其他方式
      if (item.name.includes(`Item_${itemType}_`)) {
        active++
      }
    }

    return { available, active }
  }

  /**
   * 预热指定类型的池
   */
  warmUpPool(itemType: number, count: number): void {
    if (!this.enablePooling) return

    const prefab = this.itemPrefabs[itemType]
    if (!prefab) {
      console.error(`无法预热池：物品类型 ${itemType} 的预制体不存在`)
      return
    }

    let pool = this.itemPools.get(itemType)
    if (!pool) {
      pool = []
      this.itemPools.set(itemType, pool)
    }

    const currentCount = pool.length
    const needCreate = Math.max(0, count - currentCount)

    for (let i = 0; i < needCreate; i++) {
      if (pool.length >= this.maxPoolSize) break

      const item = this.createNewItem(prefab, itemType)
      this.resetItemToPoolState(item)
      pool.push(item)
      this.poolStats.currentPooled++
    }

    console.log(`预热池完成：类型 ${itemType}，创建 ${needCreate} 个实例`)
  }

  /**
   * 输出池状态报告
   */
  logPoolReport(): void {
    console.log("=== 物品池状态报告 ===")
    console.log(`总创建: ${this.poolStats.totalCreated}`)
    console.log(`总复用: ${this.poolStats.totalReused}`)
    console.log(`总回收: ${this.poolStats.totalRecycled}`)
    console.log(`当前活跃: ${this.poolStats.currentActive}`)
    console.log(`当前池化: ${this.poolStats.currentPooled}`)
    
    console.log("各类型池状态:")
    for (const [itemType, pool] of this.itemPools) {
      const status = this.getPoolStatus(itemType)
      console.log(`  类型 ${itemType}: 可用 ${status.available}, 活跃 ${status.active}`)
    }
    
    const reuseRate = this.poolStats.totalCreated > 0 ? 
      (this.poolStats.totalReused / this.poolStats.totalCreated * 100).toFixed(1) : "0"
    console.log(`复用率: ${reuseRate}%`)
    console.log("====================")
  }
}
