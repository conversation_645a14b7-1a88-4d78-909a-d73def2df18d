[{"__type__": "cc.Prefab", "_name": "Item6", "_objFlags": 0, "__editorExtras__": {}, "_native": "", "data": {"__id__": 1}, "optimizationPolicy": 0, "persistent": false}, {"__type__": "cc.Node", "_name": "Item6", "_objFlags": 0, "__editorExtras__": {}, "_parent": null, "_children": [], "_active": true, "_components": [{"__id__": 2}, {"__id__": 4}, {"__id__": 6}, {"__id__": 8}, {"__id__": 10}], "_prefab": {"__id__": 12}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 1}, "_enabled": true, "__prefab": {"__id__": 3}, "_contentSize": {"__type__": "cc.Size", "width": 121, "height": 144}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "439QvN4yZEsIwecpUTW6T2"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 1}, "_enabled": true, "__prefab": {"__id__": 5}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "cf5243d7-0223-4253-8447-e893da1bb584@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "deTPAhvABB6YPaARvQje83"}, {"__type__": "422c98TrkdFIptzqSRWe1kP", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 1}, "_enabled": true, "__prefab": {"__id__": 7}, "sprite": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "37kCcDt3hL2qDxOmADj9ff"}, {"__type__": "cc.PolygonCollider2D", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 1}, "_enabled": true, "__prefab": {"__id__": 9}, "tag": 0, "_group": 1, "_density": 1, "_sensor": false, "_friction": 0.2, "_restitution": 0, "_offset": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_points": [{"__type__": "cc.Vec2", "x": -1.5, "y": 72}, {"__type__": "cc.Vec2", "x": -5.5, "y": 68}, {"__type__": "cc.Vec2", "x": -6.5, "y": 54}, {"__type__": "cc.Vec2", "x": -8.5, "y": 53}, {"__type__": "cc.Vec2", "x": -10.5, "y": 47}, {"__type__": "cc.Vec2", "x": -8.5, "y": 40}, {"__type__": "cc.Vec2", "x": -8.5, "y": 37}, {"__type__": "cc.Vec2", "x": -10.5, "y": 35}, {"__type__": "cc.Vec2", "x": -10.5, "y": 29}, {"__type__": "cc.Vec2", "x": -8.5, "y": 25}, {"__type__": "cc.Vec2", "x": -10.5, "y": 21}, {"__type__": "cc.Vec2", "x": -16.5, "y": 23}, {"__type__": "cc.Vec2", "x": -25.5, "y": 31}, {"__type__": "cc.Vec2", "x": -27.5, "y": 31}, {"__type__": "cc.Vec2", "x": -40.5, "y": 44}, {"__type__": "cc.Vec2", "x": -40.5, "y": 46}, {"__type__": "cc.Vec2", "x": -44.5, "y": 49}, {"__type__": "cc.Vec2", "x": -44.5, "y": 51}, {"__type__": "cc.Vec2", "x": -47.5, "y": 53}, {"__type__": "cc.Vec2", "x": -47.5, "y": 55}, {"__type__": "cc.Vec2", "x": -52.5, "y": 61}, {"__type__": "cc.Vec2", "x": -55.5, "y": 62}, {"__type__": "cc.Vec2", "x": -60.5, "y": 57}, {"__type__": "cc.Vec2", "x": -60.5, "y": 49}, {"__type__": "cc.Vec2", "x": -58.5, "y": 42}, {"__type__": "cc.Vec2", "x": -56.5, "y": 40}, {"__type__": "cc.Vec2", "x": -57.5, "y": 31}, {"__type__": "cc.Vec2", "x": -55.5, "y": 25}, {"__type__": "cc.Vec2", "x": -50.5, "y": 19}, {"__type__": "cc.Vec2", "x": -51.5, "y": 14}, {"__type__": "cc.Vec2", "x": -46.5, "y": 5}, {"__type__": "cc.Vec2", "x": -42.5, "y": 2}, {"__type__": "cc.Vec2", "x": -42.5, "y": -2}, {"__type__": "cc.Vec2", "x": -39.5, "y": -5}, {"__type__": "cc.Vec2", "x": -39.5, "y": -10}, {"__type__": "cc.Vec2", "x": -36.5, "y": -12}, {"__type__": "cc.Vec2", "x": -36.5, "y": -14}, {"__type__": "cc.Vec2", "x": -33.5, "y": -15}, {"__type__": "cc.Vec2", "x": -31.5, "y": -18}, {"__type__": "cc.Vec2", "x": -25.5, "y": -20}, {"__type__": "cc.Vec2", "x": -25.5, "y": -26}, {"__type__": "cc.Vec2", "x": -22.5, "y": -29}, {"__type__": "cc.Vec2", "x": -18.5, "y": -30}, {"__type__": "cc.Vec2", "x": -17.5, "y": -34}, {"__type__": "cc.Vec2", "x": -14.5, "y": -35}, {"__type__": "cc.Vec2", "x": -17.5, "y": -37}, {"__type__": "cc.Vec2", "x": -33.5, "y": -38}, {"__type__": "cc.Vec2", "x": -39.5, "y": -42}, {"__type__": "cc.Vec2", "x": -40.5, "y": -47}, {"__type__": "cc.Vec2", "x": -38.5, "y": -49}, {"__type__": "cc.Vec2", "x": -40.5, "y": -50}, {"__type__": "cc.Vec2", "x": -41.5, "y": -55}, {"__type__": "cc.Vec2", "x": -39.5, "y": -59}, {"__type__": "cc.Vec2", "x": -37.5, "y": -59}, {"__type__": "cc.Vec2", "x": -37.5, "y": -66}, {"__type__": "cc.Vec2", "x": -35.5, "y": -68}, {"__type__": "cc.Vec2", "x": -32.5, "y": -68}, {"__type__": "cc.Vec2", "x": -29.5, "y": -72}, {"__type__": "cc.Vec2", "x": -17.5, "y": -71}, {"__type__": "cc.Vec2", "x": -15.5, "y": -69}, {"__type__": "cc.Vec2", "x": -8.5, "y": -67}, {"__type__": "cc.Vec2", "x": -4.5, "y": -63}, {"__type__": "cc.Vec2", "x": -0.5, "y": -68}, {"__type__": "cc.Vec2", "x": 5.5, "y": -68}, {"__type__": "cc.Vec2", "x": 7.5, "y": -65}, {"__type__": "cc.Vec2", "x": 10.5, "y": -67}, {"__type__": "cc.Vec2", "x": 15.5, "y": -66}, {"__type__": "cc.Vec2", "x": 18.5, "y": -63}, {"__type__": "cc.Vec2", "x": 19.5, "y": -59}, {"__type__": "cc.Vec2", "x": 32.5, "y": -54}, {"__type__": "cc.Vec2", "x": 42.5, "y": -45}, {"__type__": "cc.Vec2", "x": 42.5, "y": -43}, {"__type__": "cc.Vec2", "x": 47.5, "y": -37}, {"__type__": "cc.Vec2", "x": 52.5, "y": -19}, {"__type__": "cc.Vec2", "x": 55.5, "y": -15}, {"__type__": "cc.Vec2", "x": 57.5, "y": -15}, {"__type__": "cc.Vec2", "x": 60.5, "y": -12}, {"__type__": "cc.Vec2", "x": 60.5, "y": -9}, {"__type__": "cc.Vec2", "x": 57.5, "y": -6}, {"__type__": "cc.Vec2", "x": 55.5, "y": -6}, {"__type__": "cc.Vec2", "x": 52.5, "y": -1}, {"__type__": "cc.Vec2", "x": 48.5, "y": 1}, {"__type__": "cc.Vec2", "x": 38.5, "y": 1}, {"__type__": "cc.Vec2", "x": 34.5, "y": -1}, {"__type__": "cc.Vec2", "x": 32.5, "y": 13}, {"__type__": "cc.Vec2", "x": 25.5, "y": 27}, {"__type__": "cc.Vec2", "x": 21.5, "y": 31}, {"__type__": "cc.Vec2", "x": 21.5, "y": 35}, {"__type__": "cc.Vec2", "x": 18.5, "y": 36}, {"__type__": "cc.Vec2", "x": 11.5, "y": 49}, {"__type__": "cc.Vec2", "x": 6.5, "y": 66}, {"__type__": "cc.Vec2", "x": 3.5, "y": 71}], "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "e8ClOPnotPBI2Q7Cox7NDz"}, {"__type__": "cc.RigidBody2D", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 1}, "_enabled": true, "__prefab": {"__id__": 11}, "enabledContactListener": false, "bullet": false, "awakeOnLoad": true, "_group": 1, "_type": 2, "_allowSleep": true, "_gravityScale": 1, "_linearDamping": 0, "_angularDamping": 0, "_linearVelocity": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_angularVelocity": 0, "_fixedRotation": false, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "c2TmyNbUJE8pYJEkiXL2nD"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "c46/YsCPVOJYA4mWEpNYRx", "instance": null, "targetOverrides": null}]