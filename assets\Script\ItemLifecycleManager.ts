import {
  _decorator,
  Component,
  Node,
  Vec3,
  RigidBody2D,
  PolygonCollider2D,
  ERigidBody2DType,
  Vec2,
  UITransform,
  game,
  director,
} from "cc"

const { ccclass, property } = _decorator

/**
 * 物品状态枚举
 */
export enum ItemState {
  DROPPING = "dropping",     // 正在掉落
  STABLE = "stable",         // 已稳定
  SLEEPING = "sleeping",     // 休眠状态
  INTERACTIVE = "interactive" // 可交互状态
}

/**
 * 物品生命周期管理器
 * 负责管理物品的激活/休眠状态，优化碰撞检测性能
 */
@ccclass("ItemLifecycleManager")
export class ItemLifecycleManager extends Component {
  
  @property({ type: Number, tooltip: "交互区域半径（像素）" })
  interactionRadius: number = 300

  @property({ type: Number, tooltip: "稳定检测时间（秒）" })
  stabilityCheckTime: number = 2.0

  @property({ type: Number, tooltip: "速度阈值，低于此值认为物品稳定" })
  stabilityVelocityThreshold: number = 5.0

  @property({ type: Number, tooltip: "休眠检测间隔（秒）" })
  sleepCheckInterval: number = 0.5

  @property({ type: Number, tooltip: "远离交互区域多久后进入休眠（秒）" })
  sleepDelay: number = 3.0

  // 管理的物品列表
  private managedItems: Map<Node, ItemLifecycleData> = new Map()
  
  // 交互中心点（通常是屏幕中心或玩家操作区域）
  private interactionCenter: Vec3 = new Vec3(0, 0, 0)
  
  // 性能统计
  private performanceStats = {
    activeColliders: 0,
    sleepingItems: 0,
    totalItems: 0,
    lastUpdateTime: 0
  }

  start() {
    // 设置交互中心点为屏幕中心
    this.updateInteractionCenter()
    
    // 开始定期检查
    this.schedule(this.performLifecycleCheck, this.sleepCheckInterval)
    
    console.log("ItemLifecycleManager 初始化完成")
  }

  onDestroy() {
    this.unschedule(this.performLifecycleCheck)
  }

  /**
   * 注册物品到生命周期管理
   */
  registerItem(itemNode: Node): void {
    if (this.managedItems.has(itemNode)) {
      return
    }

    const lifecycleData: ItemLifecycleData = {
      node: itemNode,
      state: ItemState.DROPPING,
      lastStateChangeTime: Date.now(),
      lastPosition: itemNode.position.clone(),
      stableTime: 0,
      distanceFromCenter: 0,
      rigidBody: itemNode.getComponent(RigidBody2D),
      collider: itemNode.getComponent(PolygonCollider2D),
      isColliderActive: true,
      sleepTimer: 0
    }

    this.managedItems.set(itemNode, lifecycleData)
    this.performanceStats.totalItems++
    
    console.log(`注册物品到生命周期管理: ${itemNode.name}`)
  }

  /**
   * 从生命周期管理中移除物品
   */
  unregisterItem(itemNode: Node): void {
    if (this.managedItems.has(itemNode)) {
      this.managedItems.delete(itemNode)
      this.performanceStats.totalItems--
      console.log(`从生命周期管理中移除物品: ${itemNode.name}`)
    }
  }

  /**
   * 更新交互中心点
   */
  updateInteractionCenter(center?: Vec3): void {
    if (center) {
      this.interactionCenter = center.clone()
    } else {
      // 默认使用屏幕中心
      this.interactionCenter.set(0, 0, 0)
    }
  }

  /**
   * 执行生命周期检查
   */
  private performLifecycleCheck(): void {
    const currentTime = Date.now()
    const deltaTime = (currentTime - this.performanceStats.lastUpdateTime) / 1000
    this.performanceStats.lastUpdateTime = currentTime
    
    this.performanceStats.activeColliders = 0
    this.performanceStats.sleepingItems = 0

    for (const [node, data] of this.managedItems) {
      if (!node.isValid) {
        this.managedItems.delete(node)
        continue
      }

      this.updateItemState(data, deltaTime)
      this.optimizeItemPhysics(data)
      
      // 更新统计信息
      if (data.isColliderActive) {
        this.performanceStats.activeColliders++
      }
      if (data.state === ItemState.SLEEPING) {
        this.performanceStats.sleepingItems++
      }
    }
  }

  /**
   * 更新物品状态
   */
  private updateItemState(data: ItemLifecycleData, deltaTime: number): void {
    const currentPos = data.node.position
    const distanceFromCenter = Vec3.distance(currentPos, this.interactionCenter)
    data.distanceFromCenter = distanceFromCenter

    // 检查物品是否稳定
    const positionDelta = Vec3.distance(currentPos, data.lastPosition)
    const velocity = data.rigidBody ? data.rigidBody.linearVelocity : Vec2.ZERO
    const speed = velocity.length()

    // 更新稳定时间
    if (positionDelta < 1 && speed < this.stabilityVelocityThreshold) {
      data.stableTime += deltaTime
    } else {
      data.stableTime = 0
      data.lastPosition = currentPos.clone()
    }

    // 状态转换逻辑
    switch (data.state) {
      case ItemState.DROPPING:
        if (data.stableTime >= this.stabilityCheckTime) {
          this.changeItemState(data, ItemState.STABLE)
        }
        break

      case ItemState.STABLE:
        if (distanceFromCenter <= this.interactionRadius) {
          this.changeItemState(data, ItemState.INTERACTIVE)
        } else {
          data.sleepTimer += deltaTime
          if (data.sleepTimer >= this.sleepDelay) {
            this.changeItemState(data, ItemState.SLEEPING)
          }
        }
        break

      case ItemState.INTERACTIVE:
        if (distanceFromCenter > this.interactionRadius) {
          this.changeItemState(data, ItemState.STABLE)
          data.sleepTimer = 0
        }
        break

      case ItemState.SLEEPING:
        if (distanceFromCenter <= this.interactionRadius || speed > this.stabilityVelocityThreshold) {
          this.changeItemState(data, ItemState.INTERACTIVE)
          data.sleepTimer = 0
        }
        break
    }
  }

  /**
   * 改变物品状态
   */
  private changeItemState(data: ItemLifecycleData, newState: ItemState): void {
    if (data.state === newState) return

    const oldState = data.state
    data.state = newState
    data.lastStateChangeTime = Date.now()

    console.log(`物品 ${data.node.name} 状态变化: ${oldState} -> ${newState}`)
  }

  /**
   * 优化物品物理组件
   */
  private optimizeItemPhysics(data: ItemLifecycleData): void {
    const shouldBeActive = this.shouldColliderBeActive(data)
    
    if (data.isColliderActive !== shouldBeActive) {
      this.setColliderActive(data, shouldBeActive)
    }

    // 优化刚体设置
    if (data.rigidBody) {
      switch (data.state) {
        case ItemState.DROPPING:
          // 掉落状态：正常物理
          data.rigidBody.type = ERigidBody2DType.Dynamic
          data.rigidBody.allowSleep = false
          break

        case ItemState.STABLE:
        case ItemState.INTERACTIVE:
          // 稳定/交互状态：允许休眠
          data.rigidBody.allowSleep = true
          break

        case ItemState.SLEEPING:
          // 休眠状态：强制休眠
          if (!data.rigidBody.isAwake) {
            data.rigidBody.sleep()
          }
          break
      }
    }
  }

  /**
   * 判断碰撞器是否应该激活
   */
  private shouldColliderBeActive(data: ItemLifecycleData): boolean {
    switch (data.state) {
      case ItemState.DROPPING:
        return true // 掉落时需要碰撞检测

      case ItemState.STABLE:
        return data.distanceFromCenter <= this.interactionRadius * 1.5 // 稍大的范围

      case ItemState.INTERACTIVE:
        return true // 交互状态需要碰撞检测

      case ItemState.SLEEPING:
        return false // 休眠状态不需要碰撞检测

      default:
        return true
    }
  }

  /**
   * 设置碰撞器激活状态
   */
  private setColliderActive(data: ItemLifecycleData, active: boolean): void {
    if (data.collider) {
      data.collider.enabled = active
      data.isColliderActive = active
      
      console.log(`物品 ${data.node.name} 碰撞器状态: ${active ? '激活' : '休眠'}`)
    }
  }

  /**
   * 获取性能统计信息
   */
  getPerformanceStats() {
    return { ...this.performanceStats }
  }

  /**
   * 强制唤醒指定区域内的所有物品
   */
  wakeUpItemsInRadius(center: Vec3, radius: number): void {
    for (const [node, data] of this.managedItems) {
      const distance = Vec3.distance(node.position, center)
      if (distance <= radius && data.state === ItemState.SLEEPING) {
        this.changeItemState(data, ItemState.INTERACTIVE)
        if (data.rigidBody) {
          data.rigidBody.wakeUp()
        }
      }
    }
  }
}

/**
 * 物品生命周期数据
 */
interface ItemLifecycleData {
  node: Node
  state: ItemState
  lastStateChangeTime: number
  lastPosition: Vec3
  stableTime: number
  distanceFromCenter: number
  rigidBody: RigidBody2D | null
  collider: PolygonCollider2D | null
  isColliderActive: boolean
  sleepTimer: number
}
