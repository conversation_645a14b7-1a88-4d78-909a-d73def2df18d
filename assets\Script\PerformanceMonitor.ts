import {
  _decorator,
  Component,
  Node,
  Label,
  PhysicsSystem2D,
  game,
  director,
  UITransform,
  Color,
} from "cc"

const { ccclass, property } = _decorator

/**
 * 性能监控器
 * 显示物理引擎和碰撞检测的性能信息
 */
@ccclass("PerformanceMonitor")
export class PerformanceMonitor extends Component {
  
  @property(Label)
  fpsLabel: Label = null

  @property(Label)
  physicsLabel: Label = null

  @property(Label)
  colliderLabel: Label = null

  @property(Label)
  itemLabel: Label = null

  @property({ type: Boolean, tooltip: "是否显示详细信息" })
  showDetailedInfo: boolean = true

  @property({ type: Number, tooltip: "更新间隔（秒）" })
  updateInterval: number = 0.5

  // 性能数据
  private performanceData = {
    fps: 0,
    frameTime: 0,
    physicsTime: 0,
    activeColliders: 0,
    sleepingColliders: 0,
    totalItems: 0,
    droppingItems: 0,
    stableItems: 0,
    interactiveItems: 0,
    sleepingItems: 0
  }

  // FPS计算相关
  private frameCount = 0
  private lastTime = 0
  private fpsUpdateTime = 0

  // 物理性能监控
  private physicsStartTime = 0
  private physicsEndTime = 0

  start() {
    // 初始化标签
    this.initializeLabels()
    
    // 开始监控
    this.schedule(this.updatePerformanceDisplay, this.updateInterval)
    
    // 监听物理引擎事件
    this.setupPhysicsMonitoring()
    
    console.log("PerformanceMonitor 初始化完成")
  }

  onDestroy() {
    this.unschedule(this.updatePerformanceDisplay)
  }

  update(deltaTime: number) {
    this.updateFPS(deltaTime)
  }

  /**
   * 初始化标签
   */
  private initializeLabels(): void {
    if (this.fpsLabel) {
      this.fpsLabel.string = "FPS: --"
      this.fpsLabel.color = Color.WHITE
    }

    if (this.physicsLabel) {
      this.physicsLabel.string = "Physics: --"
      this.physicsLabel.color = Color.WHITE
    }

    if (this.colliderLabel) {
      this.colliderLabel.string = "Colliders: --"
      this.colliderLabel.color = Color.WHITE
    }

    if (this.itemLabel) {
      this.itemLabel.string = "Items: --"
      this.itemLabel.color = Color.WHITE
    }
  }

  /**
   * 设置物理引擎监控
   */
  private setupPhysicsMonitoring(): void {
    // 注意：Cocos Creator的物理引擎可能没有直接的性能监控API
    // 这里我们使用间接方法来估算性能
  }

  /**
   * 更新FPS
   */
  private updateFPS(deltaTime: number): void {
    this.frameCount++
    this.fpsUpdateTime += deltaTime

    if (this.fpsUpdateTime >= 1.0) {
      this.performanceData.fps = Math.round(this.frameCount / this.fpsUpdateTime)
      this.performanceData.frameTime = (this.fpsUpdateTime / this.frameCount) * 1000 // 毫秒
      
      this.frameCount = 0
      this.fpsUpdateTime = 0
    }
  }

  /**
   * 更新性能显示
   */
  private updatePerformanceDisplay(): void {
    this.collectPerformanceData()
    this.updateLabels()
  }

  /**
   * 收集性能数据
   */
  private collectPerformanceData(): void {
    // 从ItemLifecycleManager获取数据（如果存在）
    const lifecycleManager = director.getScene()?.getComponentInChildren("ItemLifecycleManager")
    if (lifecycleManager && typeof lifecycleManager.getPerformanceStats === 'function') {
      const stats = lifecycleManager.getPerformanceStats()
      this.performanceData.activeColliders = stats.activeColliders
      this.performanceData.sleepingItems = stats.sleepingItems
      this.performanceData.totalItems = stats.totalItems
    }

    // 估算物理引擎性能
    this.estimatePhysicsPerformance()
  }

  /**
   * 估算物理引擎性能
   */
  private estimatePhysicsPerformance(): void {
    // 基于活跃碰撞器数量估算物理计算时间
    const basePhysicsTime = 0.5 // 基础物理计算时间（毫秒）
    const colliderCost = 0.1 // 每个活跃碰撞器的额外成本（毫秒）
    
    this.performanceData.physicsTime = basePhysicsTime + 
      (this.performanceData.activeColliders * colliderCost)
  }

  /**
   * 更新标签显示
   */
  private updateLabels(): void {
    // 更新FPS标签
    if (this.fpsLabel) {
      const fpsColor = this.getFPSColor(this.performanceData.fps)
      this.fpsLabel.color = fpsColor
      
      if (this.showDetailedInfo) {
        this.fpsLabel.string = `FPS: ${this.performanceData.fps} (${this.performanceData.frameTime.toFixed(1)}ms)`
      } else {
        this.fpsLabel.string = `FPS: ${this.performanceData.fps}`
      }
    }

    // 更新物理标签
    if (this.physicsLabel) {
      const physicsColor = this.getPhysicsColor(this.performanceData.physicsTime)
      this.physicsLabel.color = physicsColor
      
      if (this.showDetailedInfo) {
        this.physicsLabel.string = `Physics: ${this.performanceData.physicsTime.toFixed(1)}ms`
      } else {
        this.physicsLabel.string = `Physics: OK`
      }
    }

    // 更新碰撞器标签
    if (this.colliderLabel) {
      const colliderColor = this.getColliderColor(this.performanceData.activeColliders)
      this.colliderLabel.color = colliderColor
      
      if (this.showDetailedInfo) {
        this.colliderLabel.string = `Colliders: ${this.performanceData.activeColliders}/${this.performanceData.totalItems}`
      } else {
        this.colliderLabel.string = `Colliders: ${this.performanceData.activeColliders}`
      }
    }

    // 更新物品标签
    if (this.itemLabel) {
      if (this.showDetailedInfo) {
        this.itemLabel.string = `Items: ${this.performanceData.totalItems} (Sleep: ${this.performanceData.sleepingItems})`
      } else {
        this.itemLabel.string = `Items: ${this.performanceData.totalItems}`
      }
    }
  }

  /**
   * 获取FPS颜色
   */
  private getFPSColor(fps: number): Color {
    if (fps >= 55) return Color.GREEN
    if (fps >= 45) return Color.YELLOW
    if (fps >= 30) return new Color(255, 165, 0) // 橙色
    return Color.RED
  }

  /**
   * 获取物理性能颜色
   */
  private getPhysicsColor(physicsTime: number): Color {
    if (physicsTime <= 2) return Color.GREEN
    if (physicsTime <= 5) return Color.YELLOW
    if (physicsTime <= 10) return new Color(255, 165, 0) // 橙色
    return Color.RED
  }

  /**
   * 获取碰撞器数量颜色
   */
  private getColliderColor(activeColliders: number): Color {
    if (activeColliders <= 5) return Color.GREEN
    if (activeColliders <= 10) return Color.YELLOW
    if (activeColliders <= 15) return new Color(255, 165, 0) // 橙色
    return Color.RED
  }

  /**
   * 切换详细信息显示
   */
  toggleDetailedInfo(): void {
    this.showDetailedInfo = !this.showDetailedInfo
  }

  /**
   * 获取当前性能数据
   */
  getPerformanceData() {
    return { ...this.performanceData }
  }

  /**
   * 设置警告阈值并检查
   */
  checkPerformanceWarnings(): string[] {
    const warnings: string[] = []

    if (this.performanceData.fps < 30) {
      warnings.push("FPS过低，可能影响游戏体验")
    }

    if (this.performanceData.activeColliders > 15) {
      warnings.push("活跃碰撞器过多，建议优化")
    }

    if (this.performanceData.physicsTime > 10) {
      warnings.push("物理计算耗时过长")
    }

    return warnings
  }

  /**
   * 输出性能报告到控制台
   */
  logPerformanceReport(): void {
    console.log("=== 性能监控报告 ===")
    console.log(`FPS: ${this.performanceData.fps} (帧时间: ${this.performanceData.frameTime.toFixed(1)}ms)`)
    console.log(`物理计算: ${this.performanceData.physicsTime.toFixed(1)}ms`)
    console.log(`碰撞器: ${this.performanceData.activeColliders}/${this.performanceData.totalItems} (活跃/总数)`)
    console.log(`物品状态: 总计${this.performanceData.totalItems}, 休眠${this.performanceData.sleepingItems}`)
    
    const warnings = this.checkPerformanceWarnings()
    if (warnings.length > 0) {
      console.log("⚠️ 性能警告:")
      warnings.forEach(warning => console.log(`  - ${warning}`))
    } else {
      console.log("✅ 性能状态良好")
    }
    console.log("==================")
  }
}
