[{"__type__": "cc.Prefab", "_name": "Item4", "_objFlags": 0, "__editorExtras__": {}, "_native": "", "data": {"__id__": 1}, "optimizationPolicy": 0, "persistent": false}, {"__type__": "cc.Node", "_name": "Item4", "_objFlags": 0, "__editorExtras__": {}, "_parent": null, "_children": [], "_active": true, "_components": [{"__id__": 2}, {"__id__": 4}, {"__id__": 6}, {"__id__": 8}, {"__id__": 10}], "_prefab": {"__id__": 12}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 1}, "_enabled": true, "__prefab": {"__id__": 3}, "_contentSize": {"__type__": "cc.Size", "width": 89, "height": 87}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "439QvN4yZEsIwecpUTW6T2"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 1}, "_enabled": true, "__prefab": {"__id__": 5}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "55cf766c-08a9-432a-a221-e6bc3d53b2c7@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "deTPAhvABB6YPaARvQje83"}, {"__type__": "422c98TrkdFIptzqSRWe1kP", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 1}, "_enabled": true, "__prefab": {"__id__": 7}, "sprite": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "37kCcDt3hL2qDxOmADj9ff"}, {"__type__": "cc.PolygonCollider2D", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 1}, "_enabled": true, "__prefab": {"__id__": 9}, "tag": 0, "_group": 1, "_density": 1, "_sensor": false, "_friction": 0.2, "_restitution": 0, "_offset": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_points": [{"__type__": "cc.Vec2", "x": -8.476190476190474, "y": 43.5}, {"__type__": "cc.Vec2", "x": -9.535714285714285, "y": 41.3780487804878}, {"__type__": "cc.Vec2", "x": -22.25, "y": 39.25609756097562}, {"__type__": "cc.Vec2", "x": -21.19047619047619, "y": 37.13414634146342}, {"__type__": "cc.Vec2", "x": -23.30952380952381, "y": 38.19512195121952}, {"__type__": "cc.Vec2", "x": -24.36904761904762, "y": 33.951219512195124}, {"__type__": "cc.Vec2", "x": -26.488095238095237, "y": 32.890243902439025}, {"__type__": "cc.Vec2", "x": -29.666666666666664, "y": 33.951219512195124}, {"__type__": "cc.Vec2", "x": -28.607142857142858, "y": 31.829268292682926}, {"__type__": "cc.Vec2", "x": -31.785714285714285, "y": 28.646341463414643}, {"__type__": "cc.Vec2", "x": -33.904761904761905, "y": 29.707317073170742}, {"__type__": "cc.Vec2", "x": -33.904761904761905, "y": 23.34146341463415}, {"__type__": "cc.Vec2", "x": -38.14285714285714, "y": 19.09756097560976}, {"__type__": "cc.Vec2", "x": -40.26190476190476, "y": 20.15853658536586}, {"__type__": "cc.Vec2", "x": -39.20238095238095, "y": 16.975609756097562}, {"__type__": "cc.Vec2", "x": -41.32142857142857, "y": 18.03658536585366}, {"__type__": "cc.Vec2", "x": -41.32142857142857, "y": 8.487804878048784}, {"__type__": "cc.Vec2", "x": -43.44047619047619, "y": 8.487804878048784}, {"__type__": "cc.Vec2", "x": -42.38095238095238, "y": 6.365853658536587}, {"__type__": "cc.Vec2", "x": -44.5, "y": 7.4268292682926855}, {"__type__": "cc.Vec2", "x": -44.5, "y": -8.487804878048777}, {"__type__": "cc.Vec2", "x": -43.44047619047619, "y": -10.609756097560975}, {"__type__": "cc.Vec2", "x": -41.32142857142857, "y": -9.548780487804876}, {"__type__": "cc.Vec2", "x": -41.32142857142857, "y": -21.21951219512195}, {"__type__": "cc.Vec2", "x": -39.20238095238095, "y": -20.15853658536585}, {"__type__": "cc.Vec2", "x": -39.20238095238095, "y": -23.341463414634145}, {"__type__": "cc.Vec2", "x": -34.964285714285715, "y": -24.402439024390244}, {"__type__": "cc.Vec2", "x": -34.964285714285715, "y": -26.524390243902438}, {"__type__": "cc.Vec2", "x": -32.845238095238095, "y": -27.585365853658537}, {"__type__": "cc.Vec2", "x": -33.904761904761905, "y": -29.70731707317073}, {"__type__": "cc.Vec2", "x": -30.726190476190474, "y": -30.768292682926827}, {"__type__": "cc.Vec2", "x": -29.666666666666664, "y": -33.951219512195124}, {"__type__": "cc.Vec2", "x": -27.547619047619047, "y": -32.890243902439025}, {"__type__": "cc.Vec2", "x": -26.488095238095237, "y": -35.01219512195122}, {"__type__": "cc.Vec2", "x": -23.30952380952381, "y": -35.01219512195122}, {"__type__": "cc.Vec2", "x": -22.25, "y": -38.19512195121951}, {"__type__": "cc.Vec2", "x": -18.011904761904763, "y": -38.19512195121951}, {"__type__": "cc.Vec2", "x": -16.952380952380953, "y": -41.3780487804878}, {"__type__": "cc.Vec2", "x": -14.833333333333332, "y": -40.31707317073171}, {"__type__": "cc.Vec2", "x": -4.238095238095241, "y": -43.5}, {"__type__": "cc.Vec2", "x": 12.714285714285715, "y": -43.5}, {"__type__": "cc.Vec2", "x": 11.654761904761905, "y": -41.3780487804878}, {"__type__": "cc.Vec2", "x": 13.773809523809526, "y": -42.4390243902439}, {"__type__": "cc.Vec2", "x": 13.773809523809526, "y": -40.31707317073171}, {"__type__": "cc.Vec2", "x": 22.25, "y": -40.31707317073171}, {"__type__": "cc.Vec2", "x": 24.36904761904762, "y": -36.073170731707314}, {"__type__": "cc.Vec2", "x": 26.48809523809524, "y": -37.13414634146341}, {"__type__": "cc.Vec2", "x": 26.48809523809524, "y": -32.890243902439025}, {"__type__": "cc.Vec2", "x": 31.785714285714292, "y": -32.890243902439025}, {"__type__": "cc.Vec2", "x": 30.72619047619048, "y": -30.768292682926827}, {"__type__": "cc.Vec2", "x": 33.90476190476191, "y": -27.585365853658537}, {"__type__": "cc.Vec2", "x": 36.02380952380952, "y": -27.585365853658537}, {"__type__": "cc.Vec2", "x": 34.96428571428571, "y": -25.46341463414634}, {"__type__": "cc.Vec2", "x": 36.02380952380952, "y": -23.341463414634145}, {"__type__": "cc.Vec2", "x": 38.14285714285714, "y": -23.341463414634145}, {"__type__": "cc.Vec2", "x": 37.08333333333333, "y": -21.21951219512195}, {"__type__": "cc.Vec2", "x": 39.20238095238095, "y": -22.28048780487805}, {"__type__": "cc.Vec2", "x": 39.20238095238095, "y": -19.097560975609756}, {"__type__": "cc.Vec2", "x": 42.38095238095238, "y": -19.097560975609756}, {"__type__": "cc.Vec2", "x": 42.38095238095238, "y": -7.426829268292678}, {"__type__": "cc.Vec2", "x": 44.5, "y": -6.365853658536587}, {"__type__": "cc.Vec2", "x": 44.5, "y": 7.4268292682926855}, {"__type__": "cc.Vec2", "x": 42.38095238095238, "y": 7.4268292682926855}, {"__type__": "cc.Vec2", "x": 42.38095238095238, "y": 16.975609756097562}, {"__type__": "cc.Vec2", "x": 39.20238095238095, "y": 18.03658536585366}, {"__type__": "cc.Vec2", "x": 40.26190476190476, "y": 20.15853658536586}, {"__type__": "cc.Vec2", "x": 38.14285714285714, "y": 21.21951219512195}, {"__type__": "cc.Vec2", "x": 38.14285714285714, "y": 23.34146341463415}, {"__type__": "cc.Vec2", "x": 36.02380952380952, "y": 22.28048780487805}, {"__type__": "cc.Vec2", "x": 36.02380952380952, "y": 27.585365853658544}, {"__type__": "cc.Vec2", "x": 32.8452380952381, "y": 30.768292682926827}, {"__type__": "cc.Vec2", "x": 30.72619047619048, "y": 29.707317073170742}, {"__type__": "cc.Vec2", "x": 29.66666666666667, "y": 32.890243902439025}, {"__type__": "cc.Vec2", "x": 26.48809523809524, "y": 31.829268292682926}, {"__type__": "cc.Vec2", "x": 27.54761904761905, "y": 35.01219512195122}, {"__type__": "cc.Vec2", "x": 25.42857142857143, "y": 33.951219512195124}, {"__type__": "cc.Vec2", "x": 23.30952380952381, "y": 38.19512195121952}, {"__type__": "cc.Vec2", "x": 21.19047619047619, "y": 37.13414634146342}, {"__type__": "cc.Vec2", "x": 4.238095238095241, "y": 43.5}], "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "cdnIqU5HVK+pcclnOANdO5"}, {"__type__": "cc.RigidBody2D", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 1}, "_enabled": true, "__prefab": {"__id__": 11}, "enabledContactListener": false, "bullet": false, "awakeOnLoad": true, "_group": 1, "_type": 2, "_allowSleep": true, "_gravityScale": 1, "_linearDamping": 0, "_angularDamping": 0, "_linearVelocity": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_angularVelocity": 0, "_fixedRotation": false, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "1ea0yAViFOOLD2Ij3Z2wYB"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "c46/YsCPVOJYA4mWEpNYRx", "instance": null, "targetOverrides": null}]