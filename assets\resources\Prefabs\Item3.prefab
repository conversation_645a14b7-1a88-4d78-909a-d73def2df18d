[{"__type__": "cc.Prefab", "_name": "Item3", "_objFlags": 0, "__editorExtras__": {}, "_native": "", "data": {"__id__": 1}, "optimizationPolicy": 0, "persistent": false}, {"__type__": "cc.Node", "_name": "Item3", "_objFlags": 0, "__editorExtras__": {}, "_parent": null, "_children": [], "_active": true, "_components": [{"__id__": 2}, {"__id__": 4}, {"__id__": 6}, {"__id__": 8}, {"__id__": 10}], "_prefab": {"__id__": 12}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 1}, "_enabled": true, "__prefab": {"__id__": 3}, "_contentSize": {"__type__": "cc.Size", "width": 98, "height": 104}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "439QvN4yZEsIwecpUTW6T2"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 1}, "_enabled": true, "__prefab": {"__id__": 5}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "0e3d84fe-47b3-4e5a-b55e-f1c68960d0a0@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "deTPAhvABB6YPaARvQje83"}, {"__type__": "422c98TrkdFIptzqSRWe1kP", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 1}, "_enabled": true, "__prefab": {"__id__": 7}, "sprite": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "37kCcDt3hL2qDxOmADj9ff"}, {"__type__": "cc.PolygonCollider2D", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 1}, "_enabled": true, "__prefab": {"__id__": 9}, "tag": 0, "_group": 1, "_density": 1, "_sensor": false, "_friction": 0.2, "_restitution": 0, "_offset": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_points": [{"__type__": "cc.Vec2", "x": -3.1956521739130395, "y": 52}, {"__type__": "cc.Vec2", "x": -13.847826086956516, "y": 48.75}, {"__type__": "cc.Vec2", "x": -20.239130434782606, "y": 42.25}, {"__type__": "cc.Vec2", "x": -22.3695652173913, "y": 42.25}, {"__type__": "cc.Vec2", "x": -22.3695652173913, "y": 40.08333333333333}, {"__type__": "cc.Vec2", "x": -29.82608695652174, "y": 39}, {"__type__": "cc.Vec2", "x": -36.21739130434783, "y": 35.75}, {"__type__": "cc.Vec2", "x": -42.608695652173914, "y": 29.25}, {"__type__": "cc.Vec2", "x": -46.869565217391305, "y": 21.666666666666657}, {"__type__": "cc.Vec2", "x": -49, "y": 14.083333333333329}, {"__type__": "cc.Vec2", "x": -49, "y": -14.083333333333336}, {"__type__": "cc.Vec2", "x": -45.80434782608695, "y": -24.916666666666668}, {"__type__": "cc.Vec2", "x": -43.67391304347826, "y": -26}, {"__type__": "cc.Vec2", "x": -41.54347826086956, "y": -31.416666666666668}, {"__type__": "cc.Vec2", "x": -39.41304347826087, "y": -31.416666666666668}, {"__type__": "cc.Vec2", "x": -39.41304347826087, "y": -33.583333333333336}, {"__type__": "cc.Vec2", "x": -37.28260869565217, "y": -33.583333333333336}, {"__type__": "cc.Vec2", "x": -30.891304347826086, "y": -39}, {"__type__": "cc.Vec2", "x": -22.3695652173913, "y": -40.083333333333336}, {"__type__": "cc.Vec2", "x": -22.3695652173913, "y": -42.25}, {"__type__": "cc.Vec2", "x": -12.782608695652172, "y": -49.833333333333336}, {"__type__": "cc.Vec2", "x": -6.391304347826086, "y": -52}, {"__type__": "cc.Vec2", "x": 6.391304347826093, "y": -52}, {"__type__": "cc.Vec2", "x": 17.043478260869563, "y": -46.583333333333336}, {"__type__": "cc.Vec2", "x": 22.36956521739131, "y": -40.083333333333336}, {"__type__": "cc.Vec2", "x": 29.82608695652175, "y": -39}, {"__type__": "cc.Vec2", "x": 37.282608695652186, "y": -34.66666666666667}, {"__type__": "cc.Vec2", "x": 44.73913043478261, "y": -26}, {"__type__": "cc.Vec2", "x": 49, "y": -11.916666666666671}, {"__type__": "cc.Vec2", "x": 46.86956521739131, "y": -1.0833333333333357}, {"__type__": "cc.Vec2", "x": 49, "y": 11.916666666666664}, {"__type__": "cc.Vec2", "x": 44.73913043478261, "y": 26}, {"__type__": "cc.Vec2", "x": 41.54347826086958, "y": 28.166666666666657}, {"__type__": "cc.Vec2", "x": 41.54347826086958, "y": 30.33333333333333}, {"__type__": "cc.Vec2", "x": 29.82608695652175, "y": 39}, {"__type__": "cc.Vec2", "x": 21.304347826086968, "y": 40.08333333333333}, {"__type__": "cc.Vec2", "x": 15.978260869565219, "y": 46.58333333333333}, {"__type__": "cc.Vec2", "x": 13.847826086956523, "y": 46.58333333333333}, {"__type__": "cc.Vec2", "x": 10.652173913043484, "y": 49.83333333333333}], "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "60by2+bhpKSJQZ5n7Ycppr"}, {"__type__": "cc.RigidBody2D", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 1}, "_enabled": true, "__prefab": {"__id__": 11}, "enabledContactListener": false, "bullet": false, "awakeOnLoad": true, "_group": 1, "_type": 2, "_allowSleep": true, "_gravityScale": 1, "_linearDamping": 0, "_angularDamping": 0, "_linearVelocity": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_angularVelocity": 0, "_fixedRotation": false, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "e290Ip0yVHuIeL/osNFosT"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "c46/YsCPVOJYA4mWEpNYRx", "instance": null, "targetOverrides": null}]